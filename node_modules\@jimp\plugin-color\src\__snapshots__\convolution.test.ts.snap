// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Convolution > 3x3 sharp matrix on EDGE_CROP 1`] = `
Visualization:

86666662
68EEE82■
6E□□□8■■
6E□88■■■
6E□88■■■
688■■■■■
62■■■■■■
2■■■■■■■

Data:

88-88-88ᶠᶠ 66-66-66ᶠᶠ 66-66-66ᶠᶠ 66-66-66ᶠᶠ 66-66-66ᶠᶠ 66-66-66ᶠᶠ 66-66-66ᶠᶠ 22-22-22ᶠᶠ
66-66-66ᶠᶠ 88-88-88ᶠᶠ EE-EE-EEᶠᶠ EE-EE-EEᶠᶠ EE-EE-EEᶠᶠ 88-88-88ᶠᶠ 22-22-22ᶠᶠ 00-00-00ᶠᶠ
66-66-66ᶠᶠ EE-EE-EEᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ 88-88-88ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ
66-66-66ᶠᶠ EE-EE-EEᶠᶠ FF-FF-FFᶠᶠ 88-88-88ᶠᶠ 88-88-88ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ
66-66-66ᶠᶠ EE-EE-EEᶠᶠ FF-FF-FFᶠᶠ 88-88-88ᶠᶠ 88-88-88ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ
66-66-66ᶠᶠ 88-88-88ᶠᶠ 88-88-88ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ
66-66-66ᶠᶠ 22-22-22ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ
22-22-22ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ
`;

exports[`Convolution > 3x3 sharp matrix on EDGE_CROP 2`] = `
Visualization:

□C■66662
C■■2222■
■■■2222■
6222222■
6222222■
6222222■
6222222■
2■■■■■■■

Data:

FF-FF-FFᶠᶠ CC-CC-CCᶠᶠ 00-00-00ᶠᶠ 66-66-66ᶠᶠ 66-66-66ᶠᶠ 66-66-66ᶠᶠ 66-66-66ᶠᶠ 22-22-22ᶠᶠ
CC-CC-CCᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 00-00-00ᶠᶠ
00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 00-00-00ᶠᶠ
66-66-66ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 00-00-00ᶠᶠ
66-66-66ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 00-00-00ᶠᶠ
66-66-66ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 00-00-00ᶠᶠ
66-66-66ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 00-00-00ᶠᶠ
22-22-22ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ
`;

exports[`Convolution > 3x3 sharp matrix on EDGE_EXTEND 1`] = `
Visualization:

22222222
28EEE822
2E□□□8■2
2E□88■■2
2E□88■■2
288■■■■2
22■■■■■2
22222222

Data:

22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ 88-88-88ᶠᶠ EE-EE-EEᶠᶠ EE-EE-EEᶠᶠ EE-EE-EEᶠᶠ 88-88-88ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ EE-EE-EEᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ 88-88-88ᶠᶠ 00-00-00ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ EE-EE-EEᶠᶠ FF-FF-FFᶠᶠ 88-88-88ᶠᶠ 88-88-88ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ EE-EE-EEᶠᶠ FF-FF-FFᶠᶠ 88-88-88ᶠᶠ 88-88-88ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ 88-88-88ᶠᶠ 88-88-88ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ 22-22-22ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ
`;

exports[`Convolution > 3x3 sharp matrix on EDGE_EXTEND 2`] = `
Visualization:

8■■22222
■■■22222
■■■22222
22222222
22222222
22222222
22222222
22222222

Data:

88-88-88ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ
00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ
00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ
`;

exports[`Convolution > 3x3 sharp matrix on EDGE_WRAP 1`] = `
Visualization:

22222222
28EEE822
2E□□□8■2
2E□88■■2
2E□88■■2
288■■■■2
22■■■■■2
22222222

Data:

22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ 88-88-88ᶠᶠ EE-EE-EEᶠᶠ EE-EE-EEᶠᶠ EE-EE-EEᶠᶠ 88-88-88ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ EE-EE-EEᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ FF-FF-FFᶠᶠ 88-88-88ᶠᶠ 00-00-00ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ EE-EE-EEᶠᶠ FF-FF-FFᶠᶠ 88-88-88ᶠᶠ 88-88-88ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ EE-EE-EEᶠᶠ FF-FF-FFᶠᶠ 88-88-88ᶠᶠ 88-88-88ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ 88-88-88ᶠᶠ 88-88-88ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ 22-22-22ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ
`;

exports[`Convolution > 3x3 sharp matrix on EDGE_WRAP 2`] = `
Visualization:

□8■2222E
8■■22228
■■■22222
22222222
22222222
22222222
22222222
E8222228

Data:

FF-FF-FFᶠᶠ 88-88-88ᶠᶠ 00-00-00ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ EE-EE-EEᶠᶠ
88-88-88ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 88-88-88ᶠᶠ
00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ
EE-EE-EEᶠᶠ 88-88-88ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 88-88-88ᶠᶠ
`;
