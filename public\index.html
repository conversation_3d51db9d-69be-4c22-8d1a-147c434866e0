<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="GIFCraft - Generate stunning GIFs from text, images, or videos using AI for free.">
  <meta property="og:title" content="GIFCraft - AI GIF Generator">
  <meta property="og:description"
    content="Generate high-quality animated GIFs using text prompts, images, or videos. Free, fast, and beautiful.">
  <meta property="og:image" content="/favicon.png">
  <title>GIFCraft - AI Powered GIF Generator Tool</title>
  <link rel="icon" href="/favicon.png">
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    .loader {
      border: 3px solid #f3f4f6;
      border-top: 3px solid #6366f1;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      animation: spin 1s linear infinite;
    }

    @media (min-width: 640px) {
      .loader {
        border-width: 4px;
        width: 60px;
        height: 60px;
      }
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    .progress-step {
      transition: all 0.3s ease;
    }

    .progress-step.active {
      background-color: #dbeafe !important;
      border-left-color: #3b82f6 !important;
      opacity: 1 !important;
    }

    .progress-step.completed {
      background-color: #dcfce7 !important;
      border-left-color: #22c55e !important;
      opacity: 1 !important;
    }

    .progress-step.error {
      background-color: #fef2f2 !important;
      border-left-color: #ef4444 !important;
      opacity: 1 !important;
    }

    /* Custom scrollbar for textarea */
    textarea::-webkit-scrollbar {
      width: 8px;
    }

    textarea::-webkit-scrollbar-track {
      background: #f1f5f9;
      border-radius: 4px;
    }

    textarea::-webkit-scrollbar-thumb {
      background: #cbd5e1;
      border-radius: 4px;
    }

    textarea::-webkit-scrollbar-thumb:hover {
      background: #94a3b8;
    }

    /* Smooth animations */
    .transform {
      transition: transform 0.2s ease;
    }

    /* Gradient text effect */
    .gradient-text {
      background: linear-gradient(135deg, #6366f1, #a855f7);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    /* Mobile-specific improvements */
    @media (max-width: 640px) {

      /* Prevent horizontal scroll */
      body {
        overflow-x: hidden;
      }

      /* Better touch targets */
      button,
      a {
        min-height: 44px;
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
      }

      /* Improved text selection on mobile */
      textarea {
        -webkit-user-select: text;
        user-select: text;
      }

      /* Better focus states for mobile */
      textarea:focus,
      button:focus {
        outline: 2px solid #6366f1;
        outline-offset: 2px;
      }
    }

    /* Prevent zoom on input focus (iOS) */
    @media screen and (max-width: 640px) {

      input,
      textarea,
      select {
        font-size: 16px !important;
      }

      /* Ensure buttons are clickable on mobile */
      button {
        cursor: pointer;
        -webkit-user-select: none;
        user-select: none;
        position: relative;
        z-index: 1;
      }

      /* Remove any pointer-events issues */
      button * {
        pointer-events: none;
      }
    }
  </style>
</head>

<body class="bg-gradient-to-br from-gray-50 to-purple-50 text-gray-800 font-sans">
  <header class="bg-gradient-to-r from-indigo-500 to-purple-600 py-4 sm:py-6 shadow-md border-b border-gray-200">
    <div class="max-w-5xl mx-auto px-3 sm:px-4 text-center">
      <h1 class="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-extrabold text-white drop-shadow-sm">GIFCraft</h1>
      <p class="mt-1 sm:mt-2 text-xs sm:text-sm md:text-base lg:text-lg text-indigo-100 px-2">✨ Free AI-Powered GIF
        Generator ✨</p>
    </div>
  </header>

  <main class="max-w-4xl mx-auto px-3 sm:px-4 py-6 sm:py-8 lg:py-12">
    <!-- Text to GIF Section -->
    <div class="bg-white rounded-xl sm:rounded-2xl shadow-xl p-4 sm:p-6 lg:p-8 mb-6 sm:mb-8">
      <div class="text-center mb-6 sm:mb-8">
        <h2 class="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-800 mb-2">✨ Text to GIF Generator</h2>
        <p class="text-sm sm:text-base text-gray-600 px-2">Transform your imagination into animated GIFs using AI</p>
      </div>

      <div class="space-y-4 sm:space-y-6">
        <div>
          <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-3 gap-2">
            <label class="text-base sm:text-lg font-semibold text-gray-700">🎨 Describe your GIF</label>
            <button type="button" id="sampleBtn"
              class="text-xs sm:text-sm bg-indigo-100 hover:bg-indigo-200 text-indigo-700 px-3 py-1.5 sm:py-1 rounded-lg transition-colors self-start sm:self-auto min-h-[44px] flex items-center justify-center">
              ✨ Try Example
            </button>
          </div>
          <textarea id="textPrompt"
            placeholder="E.g. A neon wolf running across a cyberpunk cityscape..."
            rows="3"
            class="w-full p-3 sm:p-4 rounded-lg sm:rounded-xl border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent resize-none text-gray-700 placeholder-gray-400 text-sm sm:text-base"></textarea>
          <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mt-2 gap-1 sm:gap-2">
            <p class="text-xs sm:text-sm text-gray-500 order-2 sm:order-1">💡 Tip: Be descriptive! The more details you
              provide, the better your GIF will be.</p>
            <span id="charCounter" class="text-xs text-gray-400 order-1 sm:order-2 self-end sm:self-auto">0/500</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Generate Button -->
    <div class="text-center px-2">
      <button id="generateBtn"
        class="w-full sm:w-auto bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-6 sm:px-8 py-3 sm:py-4 rounded-xl sm:rounded-2xl font-bold text-base sm:text-lg shadow-xl transition-all transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none">
        <span id="btnText">🚀 Generate Amazing GIF</span>
        <span id="btnLoader" class="hidden">⏳ Generating...</span>
      </button>
      <p class="text-xs sm:text-sm text-gray-500 mt-2 sm:mt-3 px-2">✨ Free to use • No signup required • High quality
        results</p>

      <!-- Error Message Section -->
      <div id="errorSection" class="mt-4 hidden">
        <div class="bg-red-50 border border-red-200 rounded-lg sm:rounded-xl p-4 sm:p-6 max-w-2xl mx-auto">
          <div class="flex items-start">
            <div class="flex-shrink-0">
              <span class="text-red-500 text-xl sm:text-2xl">⚠️</span>
            </div>
            <div class="ml-3 flex-1">
              <h3 class="text-sm sm:text-base font-semibold text-red-800 mb-1">Oops! Something went wrong</h3>
              <p id="errorMessage" class="text-xs sm:text-sm text-red-700 leading-relaxed"></p>
              <div class="mt-3 flex flex-col sm:flex-row gap-2">
                <button id="retryBtn"
                  class="inline-flex items-center justify-center px-3 py-1.5 sm:px-4 sm:py-2 bg-red-100 hover:bg-red-200 text-red-800 text-xs sm:text-sm font-medium rounded-lg transition-colors">
                  <span class="mr-1">🔄</span>
                  Try Again
                </button>
                <button id="dismissErrorBtn"
                  class="inline-flex items-center justify-center px-3 py-1.5 sm:px-4 sm:py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 text-xs sm:text-sm font-medium rounded-lg transition-colors">
                  <span class="mr-1">✕</span>
                  Dismiss
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading Section -->
    <div id="loadingSection" class="mt-8 sm:mt-12 hidden">
      <div class="bg-white rounded-xl sm:rounded-2xl shadow-xl p-4 sm:p-6 lg:p-8">
        <div class="text-center mb-4 sm:mb-6">
          <h2 class="text-lg sm:text-xl lg:text-2xl font-bold text-gray-800 mb-2">⏳ Creating Your Masterpiece...</h2>
          <p class="text-sm sm:text-base text-gray-600 px-2">Our AI is working hard to bring your imagination to life!
          </p>
        </div>

        <div class="flex justify-center mb-4 sm:mb-6">
          <div class="loader"></div>
        </div>

        <div id="progressSteps" class="space-y-2 sm:space-y-3">
          <div id="step1"
            class="progress-step p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl border-l-4 border-gray-300">
            <div class="flex items-center">
              <span class="text-lg sm:text-xl lg:text-2xl mr-2 sm:mr-3">⏳</span>
              <span class="text-sm sm:text-base">Step 1: Generating unique images from your prompt...</span>
            </div>
          </div>
          <div id="step2"
            class="progress-step p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl border-l-4 border-gray-300 opacity-50">
            <div class="flex items-center">
              <span class="text-lg sm:text-xl lg:text-2xl mr-2 sm:mr-3">⌛</span>
              <span class="text-sm sm:text-base">Step 2: Creating smooth animated transitions...</span>
            </div>
          </div>
          <div id="step3"
            class="progress-step p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl border-l-4 border-gray-300 opacity-50">
            <div class="flex items-center">
              <span class="text-lg sm:text-xl lg:text-2xl mr-2 sm:mr-3">⌛</span>
              <span class="text-sm sm:text-base">Step 3: Optimizing and finalizing your GIF...</span>
            </div>
          </div>
        </div>

        <div class="mt-4 sm:mt-6 text-center">
          <p class="text-xs sm:text-sm text-gray-500">⏱️ This usually takes 30-60 seconds</p>
        </div>
      </div>
    </div>

    <!-- Output Section -->
    <div id="outputSection" class="mt-8 sm:mt-12 hidden">
      <div class="bg-white rounded-xl sm:rounded-2xl shadow-xl p-4 sm:p-6 lg:p-8 text-center">
        <h2 class="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-800 mb-2">🎉 Your GIF is Ready!</h2>
        <p class="text-sm sm:text-base text-gray-600 mb-4 sm:mb-6 px-2">Here's your amazing AI-generated animated GIF
        </p>

        <div class="mb-4 sm:mb-6">
          <img id="gifPreview" src="" alt="Generated GIF"
            class="w-full max-w-sm sm:max-w-md lg:max-w-lg mx-auto rounded-lg sm:rounded-xl shadow-lg border-2 sm:border-4 border-gray-100">
        </div>

        <div class="flex flex-col gap-3 sm:gap-4 justify-center items-center">
          <a id="downloadBtn" href="#" download
            class="w-full sm:w-auto inline-flex items-center justify-center bg-green-500 hover:bg-green-600 text-white px-4 sm:px-6 py-2.5 sm:py-3 rounded-lg sm:rounded-xl font-bold shadow-lg transition-all transform hover:scale-105 text-sm sm:text-base">
            <span class="text-lg sm:text-xl mr-2">⬇️</span>
            Download GIF
          </a>

          <div class="flex flex-col sm:flex-row gap-3 sm:gap-4 w-full sm:w-auto">
            <button id="shareBtn"
              class="w-full sm:w-auto inline-flex items-center justify-center bg-blue-500 hover:bg-blue-600 text-white px-4 sm:px-6 py-2.5 sm:py-3 rounded-lg sm:rounded-xl font-bold shadow-lg transition-all transform hover:scale-105 text-sm sm:text-base">
              <span class="text-lg sm:text-xl mr-2">📤</span>
              Share GIF
            </button>

            <button id="newGifBtn"
              class="w-full sm:w-auto inline-flex items-center justify-center bg-purple-500 hover:bg-purple-600 text-white px-4 sm:px-6 py-2.5 sm:py-3 rounded-lg sm:rounded-xl font-bold shadow-lg transition-all transform hover:scale-105 text-sm sm:text-base">
              <span class="text-lg sm:text-xl mr-2">✨</span>
              Create Another
            </button>
          </div>
        </div>

        <div class="mt-4 sm:mt-6 p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl">
          <p class="text-xs sm:text-sm text-gray-600">
            💡 <strong>Love your GIF?</strong> Share it with friends or use it on social media!
          </p>
        </div>
      </div>
    </div>
  </main>

  <footer class="text-center text-xs sm:text-sm text-gray-500 py-6 sm:py-8 px-4">
    &copy; 2025 <span class="font-semibold text-purple-600">GIFCraft</span>. Powered by Affan AI. All rights reserved.
  </footer>
  <script src="./script.js"></script>
</body>

</html>