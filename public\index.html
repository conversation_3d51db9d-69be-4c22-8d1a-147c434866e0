<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="GIFCraft - Generate stunning GIFs from text, images, or videos using AI for free.">
  <meta property="og:title" content="GIFCraft - AI GIF Generator">
  <meta property="og:description"
    content="Generate high-quality animated GIFs using text prompts, images, or videos. Free, fast, and beautiful.">
  <meta property="og:image" content="/favicon.png">
  <title>GIFCraft - AI Powered GIF Generator Tool</title>
  <link rel="icon" href="/favicon.png">
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    .loader {
      border: 4px solid #f3f4f6;
      border-top: 4px solid #6366f1;
      border-radius: 50%;
      width: 60px;
      height: 60px;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    .progress-step {
      transition: all 0.3s ease;
    }

    .progress-step.active {
      background-color: #dbeafe !important;
      border-left-color: #3b82f6 !important;
      opacity: 1 !important;
    }

    .progress-step.completed {
      background-color: #dcfce7 !important;
      border-left-color: #22c55e !important;
      opacity: 1 !important;
    }

    .progress-step.error {
      background-color: #fef2f2 !important;
      border-left-color: #ef4444 !important;
      opacity: 1 !important;
    }

    /* Custom scrollbar for textarea */
    textarea::-webkit-scrollbar {
      width: 8px;
    }

    textarea::-webkit-scrollbar-track {
      background: #f1f5f9;
      border-radius: 4px;
    }

    textarea::-webkit-scrollbar-thumb {
      background: #cbd5e1;
      border-radius: 4px;
    }

    textarea::-webkit-scrollbar-thumb:hover {
      background: #94a3b8;
    }

    /* Smooth animations */
    .transform {
      transition: transform 0.2s ease;
    }

    /* Gradient text effect */
    .gradient-text {
      background: linear-gradient(135deg, #6366f1, #a855f7);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  </style>
</head>

<body class="bg-gradient-to-br from-gray-50 to-purple-50 text-gray-800 font-sans">
  <header class="bg-gradient-to-r from-indigo-500 to-purple-600 py-6 shadow-md border-b border-gray-200">
    <div class="max-w-5xl mx-auto px-4 text-center">
      <h1 class="text-4xl md:text-5xl font-extrabold text-white drop-shadow-sm">GIFCraft</h1>
      <p class="mt-2 text-sm md:text-lg text-indigo-100">✨ Free AI-Powered GIF Generator - Text, Image, or Video to GIF
        ✨</p>
    </div>
  </header>

  <main class="max-w-4xl mx-auto px-4 py-12">
    <!-- Text to GIF Section -->
    <div class="bg-white rounded-2xl shadow-xl p-8 mb-8">
      <div class="text-center mb-8">
        <h2 class="text-3xl font-bold text-gray-800 mb-2">✨ Text to GIF Generator</h2>
        <p class="text-gray-600">Transform your imagination into animated GIFs using AI</p>
      </div>

      <div class="space-y-6">
        <div>
          <div class="flex justify-between items-center mb-3">
            <label class="text-lg font-semibold text-gray-700">🎨 Describe your GIF</label>
            <button type="button" onclick="addSamplePrompt()"
              class="text-sm bg-indigo-100 hover:bg-indigo-200 text-indigo-700 px-3 py-1 rounded-lg transition-colors">
              ✨ Try Example
            </button>
          </div>
          <textarea id="textPrompt"
            placeholder="E.g. A neon tiger walking through a futuristic jungle, glowing butterflies flying around a magical forest, a robot dancing in space..."
            rows="4"
            class="w-full p-4 rounded-xl border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent resize-none text-gray-700 placeholder-gray-400"></textarea>
          <div class="flex justify-between items-center mt-2">
            <p class="text-sm text-gray-500">💡 Tip: Be descriptive! The more details you provide, the better your GIF
              will be.</p>
            <span id="charCounter" class="text-xs text-gray-400">0/500</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Generate Button -->
    <div class="text-center">
      <button id="generateBtn"
        class="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-8 py-4 rounded-2xl font-bold text-lg shadow-xl transition-all transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none">
        <span id="btnText">🚀 Generate Amazing GIF</span>
        <span id="btnLoader" class="hidden">⏳ Generating...</span>
      </button>
      <p class="text-sm text-gray-500 mt-3">✨ Free to use • No signup required • High quality results</p>
    </div>

    <!-- Loading Section -->
    <div id="loadingSection" class="mt-12 hidden">
      <div class="bg-white rounded-2xl shadow-xl p-8">
        <div class="text-center mb-6">
          <h2 class="text-2xl font-bold text-gray-800 mb-2">⏳ Creating Your Masterpiece...</h2>
          <p class="text-gray-600">Our AI is working hard to bring your imagination to life!</p>
        </div>

        <div class="flex justify-center mb-6">
          <div class="loader"></div>
        </div>

        <div id="progressSteps" class="space-y-3">
          <div id="step1" class="progress-step p-4 bg-gray-50 rounded-xl border-l-4 border-gray-300">
            <div class="flex items-center">
              <span class="text-2xl mr-3">⏳</span>
              <span>Step 1: Generating unique images from your prompt...</span>
            </div>
          </div>
          <div id="step2" class="progress-step p-4 bg-gray-50 rounded-xl border-l-4 border-gray-300 opacity-50">
            <div class="flex items-center">
              <span class="text-2xl mr-3">⌛</span>
              <span>Step 2: Creating smooth animated transitions...</span>
            </div>
          </div>
          <div id="step3" class="progress-step p-4 bg-gray-50 rounded-xl border-l-4 border-gray-300 opacity-50">
            <div class="flex items-center">
              <span class="text-2xl mr-3">⌛</span>
              <span>Step 3: Optimizing and finalizing your GIF...</span>
            </div>
          </div>
        </div>

        <div class="mt-6 text-center">
          <p class="text-sm text-gray-500">⏱️ This usually takes 30-60 seconds</p>
        </div>
      </div>
    </div>

    <!-- Output Section -->
    <div id="outputSection" class="mt-12 hidden">
      <div class="bg-white rounded-2xl shadow-xl p-8 text-center">
        <h2 class="text-3xl font-bold text-gray-800 mb-2">🎉 Your GIF is Ready!</h2>
        <p class="text-gray-600 mb-6">Here's your amazing AI-generated animated GIF</p>

        <div class="mb-6">
          <img id="gifPreview" src="" alt="Generated GIF"
            class="w-full max-w-lg mx-auto rounded-xl shadow-lg border-4 border-gray-100">
        </div>

        <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <a id="downloadBtn" href="#" download
            class="inline-flex items-center bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-xl font-bold shadow-lg transition-all transform hover:scale-105">
            <span class="text-xl mr-2">⬇️</span>
            Download GIF
          </a>

          <button id="shareBtn"
            class="inline-flex items-center bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-xl font-bold shadow-lg transition-all transform hover:scale-105">
            <span class="text-xl mr-2">📤</span>
            Share GIF
          </button>

          <button id="newGifBtn"
            class="inline-flex items-center bg-purple-500 hover:bg-purple-600 text-white px-6 py-3 rounded-xl font-bold shadow-lg transition-all transform hover:scale-105">
            <span class="text-xl mr-2">✨</span>
            Create Another
          </button>
        </div>

        <div class="mt-6 p-4 bg-gray-50 rounded-xl">
          <p class="text-sm text-gray-600">
            💡 <strong>Love your GIF?</strong> Share it with friends or use it on social media!
          </p>
        </div>
      </div>
    </div>
  </main>

  <footer class="text-center text-sm text-gray-500 py-8">
    &copy; 2025 <span class="font-semibold text-purple-600">GIFCraft</span>. Powered by Affan AI. All rights reserved.
  </footer>
  <script src="./script.js"></script>
</body>

</html>