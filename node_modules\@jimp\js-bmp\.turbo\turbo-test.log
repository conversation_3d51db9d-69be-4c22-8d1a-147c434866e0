

> @jimp/js-bmp@1.1.2 test /Users/<USER>/Documents/jimp/plugins/js-bmp
> vitest "--watch=false"


[7m[1m[36m RUN [39m[22m[27m [36mv2.0.5[39m [90m/Users/<USER>/Documents/jimp/plugins/js-bmp[39m

[?25l [90m·[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
[?25l[?25l[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [90m·[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
[?25l[?25l[?25l[?25l[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠙[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠹[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠸[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠼[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠴[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠦[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠧[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠇[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠏[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠋[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠙[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠹[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠸[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠼[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠴[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠦[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠧[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠇[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠏[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠋[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠙[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠹[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠸[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠼[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠴[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠦[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠧[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠇[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠏[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠋[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠙[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠹[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠸[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠼[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠴[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠦[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠧[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠇[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠏[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠋[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠙[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠹[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠸[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠼[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠴[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠦[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠧[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠇[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠏[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠋[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠙[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠹[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠸[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠼[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠴[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠦[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠧[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠇[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠏[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠋[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠙[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠹[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠸[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠼[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠴[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠦[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠧[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠇[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠏[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠋[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠙[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠹[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠸[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠼[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠴[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠦[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠧[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠇[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠏[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠋[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠙[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠹[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠸[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠼[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠴[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠦[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠧[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠇[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠏[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠋[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠙[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠹[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠸[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠼[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠴[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠦[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠧[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠇[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠏[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠋[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠙[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠹[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠸[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠼[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠴[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠦[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠧[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠇[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠏[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠋[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠙[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠹[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠸[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠼[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠴[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠦[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠧[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠇[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠏[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠋[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠙[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠹[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠸[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠼[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠴[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠦[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠧[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠇[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠏[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠋[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠙[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠹[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠸[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠼[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠴[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠦[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠧[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠇[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠏[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠋[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠙[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠹[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠸[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠼[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠴[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠦[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠧[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠇[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠏[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠋[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠙[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠹[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠸[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠼[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠴[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠦[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠧[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠇[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠏[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠋[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠙[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠹[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠸[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠼[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠴[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠦[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠧[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠇[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠏[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠋[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠙[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠹[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠸[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠼[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠴[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠦[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠧[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠇[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠏[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠋[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠙[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠹[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠸[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠼[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠴[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠦[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠧[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠇[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠏[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠋[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠙[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠹[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠸[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠼[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠴[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠦[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠧[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠇[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠏[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠋[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠙[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠹[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠸[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠼[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠴[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠦[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠧[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠇[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠏[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠋[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠙[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠹[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠸[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠼[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠴[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠦[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠧[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠇[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠏[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠋[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠙[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠹[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠸[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠼[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠴[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠦[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠧[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠇[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠏[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠋[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠙[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠹[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠸[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠼[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠴[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠦[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠧[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠇[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠏[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠋[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠙[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠹[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠸[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠼[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠴[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠦[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠧[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠇[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠏[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠋[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠙[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠹[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠸[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠼[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠴[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠦[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠧[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠇[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠏[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠋[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠙[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠹[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠸[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠼[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [33m❯[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m
   [33m❯[39m BMP[2m (1)[22m
     [33m⠴[39m uses correct colors for BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
 [32m✓[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m[33m 5101[2mms[22m[39m
[?25l[?25l[?25l[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.node.test.ts[22m[2m (1)[22m[33m 5101[2mms[22m[39m
 [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m

[2m Test Files [22m [1m[32m2 passed[39m[22m[90m (2)[39m
[2m      Tests [22m [1m[32m3 passed[39m[22m[90m (3)[39m
[2m   Start at [22m 01:33:51
[2m   Duration [22m 7.17s[2m (transform 705ms, setup 0ms, collect 2.20s, tests 5.13s, environment 0ms, prepare 606ms)[22m

[?25h[?25h
