// DOM Elements
const textPrompt = document.getElementById("textPrompt");
const generateBtn = document.getElementById("generateBtn");
const btnText = document.getElementById("btnText");
const btnLoader = document.getElementById("btnLoader");
const loadingSection = document.getElementById("loadingSection");
const outputSection = document.getElementById("outputSection");
const gifPreview = document.getElementById("gifPreview");
const downloadBtn = document.getElementById("downloadBtn");
const shareBtn = document.getElementById("shareBtn");
const newGifBtn = document.getElementById("newGifBtn");

// Progress step elements
const step1 = document.getElementById("step1");
const step2 = document.getElementById("step2");
const step3 = document.getElementById("step3");

// Sample prompts for inspiration
const samplePrompts = [
    "A majestic dragon flying through clouds with lightning in the background",
    "A robot dancing in a neon-lit cyberpunk city at night",
    "Glowing butterflies flying around a magical enchanted forest",
    "A cute cat wearing sunglasses riding a skateboard",
    "Ocean waves crashing against rocks during a beautiful sunset",
    "A spaceship traveling through a colorful galaxy with stars",
    "A phoenix rising from flames with golden feathers",
    "A wizard casting spells with magical sparkles and energy",
    "Cherry blossoms falling in a peaceful Japanese garden",
    "A futuristic car racing through a neon tunnel"
];

// Add sample prompt functionality
function addSamplePrompt() {
    const randomPrompt = samplePrompts[Math.floor(Math.random() * samplePrompts.length)];
    textPrompt.value = randomPrompt;
    textPrompt.focus();
}

// Utility functions
function resetProgressSteps() {
    const isMobile = window.innerWidth < 640;
    const stepClass = isMobile ?
        "progress-step p-3 bg-gray-50 rounded-lg border-l-4 border-gray-300" :
        "progress-step p-4 bg-gray-50 rounded-xl border-l-4 border-gray-300";
    const iconSize = isMobile ? "text-lg" : "text-2xl";
    const textSize = isMobile ? "text-sm" : "text-base";

    step1.className = stepClass;
    step2.className = stepClass + " opacity-50";
    step3.className = stepClass + " opacity-50";

    step1.innerHTML = `<div class="flex items-center"><span class="${iconSize} mr-2 sm:mr-3">⏳</span><span class="${textSize}">Step 1: Generating unique images from your prompt...</span></div>`;
    step2.innerHTML = `<div class="flex items-center"><span class="${iconSize} mr-2 sm:mr-3">⌛</span><span class="${textSize}">Step 2: Creating smooth animated transitions...</span></div>`;
    step3.innerHTML = `<div class="flex items-center"><span class="${iconSize} mr-2 sm:mr-3">⌛</span><span class="${textSize}">Step 3: Optimizing and finalizing your GIF...</span></div>`;
}

function updateProgressStep(stepElement, status, text, icon) {
    const isMobile = window.innerWidth < 640;
    const stepClass = isMobile ?
        "progress-step p-3 bg-gray-50 rounded-lg border-l-4 border-gray-300" :
        "progress-step p-4 bg-gray-50 rounded-xl border-l-4 border-gray-300";
    const iconSize = isMobile ? "text-lg" : "text-2xl";
    const textSize = isMobile ? "text-sm" : "text-base";

    stepElement.className = `${stepClass} ${status}`;
    stepElement.innerHTML = `<div class="flex items-center"><span class="${iconSize} mr-2 sm:mr-3">${icon}</span><span class="${textSize}">${text}</span></div>`;
}

function setButtonLoading(loading) {
    generateBtn.disabled = loading;
    if (loading) {
        btnText.classList.add("hidden");
        btnLoader.classList.remove("hidden");
    } else {
        btnText.classList.remove("hidden");
        btnLoader.classList.add("hidden");
    }
}

function validatePrompt(prompt) {
    if (!prompt) {
        alert("Please enter a description for your GIF!");
        textPrompt.focus();
        return false;
    }

    if (prompt.length < 10) {
        alert("Please provide a more detailed description (at least 10 characters).");
        textPrompt.focus();
        return false;
    }

    if (prompt.length > 500) {
        alert("Please keep your description under 500 characters.");
        textPrompt.focus();
        return false;
    }

    return true;
}

// Handle Generate Button Click
generateBtn.addEventListener("click", async () => {
    const prompt = textPrompt.value.trim();

    // Validate input
    if (!validatePrompt(prompt)) return;

    // Reset UI state
    outputSection.classList.add("hidden");
    gifPreview.src = "";
    downloadBtn.href = "#";
    resetProgressSteps();
    setButtonLoading(true);

    try {
        // Show loading section
        loadingSection.classList.remove("hidden");

        // Update step 1
        updateProgressStep(step1, "active", "Generating unique images from your prompt...", "⏳");

        // Prepare request data
        const requestData = {
            prompt: prompt
        };

        // Make API request
        const res = await fetch("/api/text-to-gif", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(requestData),
        });

        const data = await res.json();

        if (res.ok && data.gifUrl) {
            // Update progress steps for success
            updateProgressStep(step1, "completed", "3 unique images generated successfully!", "✅");
            updateProgressStep(step2, "completed", "Animated GIF created successfully!", "✅");
            updateProgressStep(step3, "completed", "Multi-frame animation ready!", "✅");

            // Hide loading section
            loadingSection.classList.add("hidden");
            setButtonLoading(false);

            // Show output section with the generated GIF
            gifPreview.src = data.gifUrl;
            downloadBtn.href = data.gifUrl;
            downloadBtn.setAttribute("download", `gifcraft-${Date.now()}.gif`);
            outputSection.classList.remove("hidden");

            // Scroll to the output section
            setTimeout(() => {
                outputSection.scrollIntoView({ behavior: 'smooth' });
            }, 500);

        } else {
            // Handle API error
            loadingSection.classList.add("hidden");
            setButtonLoading(false);

            const errorMessage = data.details || data.error || "Failed to generate GIF";
            alert(`Error: ${errorMessage}`);
            console.error("API Error:", data);

            // Update progress steps for error
            updateProgressStep(step1, "error", "Error generating images. Please try again.", "❌");
        }

    } catch (err) {
        // Handle network/other errors
        loadingSection.classList.add("hidden");
        setButtonLoading(false);

        console.error("Network Error:", err);
        alert("Something went wrong while generating your GIF. Please check your connection and try again.");

        // Update progress steps for error
        updateProgressStep(step1, "error", `Error: ${err.message || "Unknown error"}`, "❌");
    }
});

// Handle Share Button
shareBtn.addEventListener("click", async () => {
    if (navigator.share && gifPreview.src) {
        try {
            await navigator.share({
                title: 'Check out my AI-generated GIF!',
                text: 'I created this amazing GIF using GIFCraft - AI Powered GIF Generator',
                url: window.location.href
            });
        } catch (err) {
            console.log('Error sharing:', err);
            copyToClipboard(window.location.href);
        }
    } else {
        copyToClipboard(window.location.href);
    }
});

// Handle New GIF Button
newGifBtn.addEventListener("click", () => {
    // Reset form
    textPrompt.value = "";

    // Reset character counter
    charCounter.textContent = '0/500';
    charCounter.className = 'text-xs text-gray-400';

    // Reset textarea height
    textPrompt.style.height = 'auto';

    // Hide output section
    outputSection.classList.add("hidden");

    // Focus on prompt input
    textPrompt.focus();

    // Scroll to top
    window.scrollTo({ top: 0, behavior: 'smooth' });
});

// Utility function to copy to clipboard
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        alert('Link copied to clipboard!');
    }).catch(() => {
        alert('Unable to copy link. Please copy manually: ' + text);
    });
}

// Add keyboard shortcuts
document.addEventListener('keydown', (e) => {
    // Ctrl/Cmd + Enter to generate
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        e.preventDefault();
        if (!generateBtn.disabled) {
            generateBtn.click();
        }
    }

    // Escape to cancel/reset
    if (e.key === 'Escape') {
        if (!loadingSection.classList.contains('hidden')) {
            // Could add cancel functionality here
        } else if (!outputSection.classList.contains('hidden')) {
            newGifBtn.click();
        }
    }
});

// Character counter and auto-resize functionality
const charCounter = document.getElementById('charCounter');

textPrompt.addEventListener('input', function () {
    const length = this.value.length;
    const maxLength = 500;

    // Update character counter
    charCounter.textContent = `${length}/${maxLength}`;

    // Change color based on length
    if (length > maxLength * 0.9) {
        charCounter.className = 'text-xs text-red-500';
    } else if (length > maxLength * 0.7) {
        charCounter.className = 'text-xs text-yellow-500';
    } else {
        charCounter.className = 'text-xs text-gray-400';
    }

    // Prevent exceeding max length
    if (length > maxLength) {
        this.value = this.value.substring(0, maxLength);
        charCounter.textContent = `${maxLength}/${maxLength}`;
        charCounter.className = 'text-xs text-red-500';
    }

    // Auto-resize textarea
    this.style.height = 'auto';
    this.style.height = this.scrollHeight + 'px';
});

// Initialize character counter
charCounter.textContent = '0/500';

// Mobile-specific improvements
function handleMobileResize() {
    // Reset progress steps on orientation change
    if (!loadingSection.classList.contains('hidden')) {
        resetProgressSteps();
    }
}

// Handle orientation change and resize
window.addEventListener('orientationchange', () => {
    setTimeout(handleMobileResize, 100);
});

window.addEventListener('resize', handleMobileResize);

// Prevent double-tap zoom on buttons (mobile)
document.addEventListener('touchend', function (e) {
    if (e.target.tagName === 'BUTTON' || e.target.closest('button')) {
        e.preventDefault();
    }
});

// Better mobile scroll behavior
function scrollToElement(element, offset = 20) {
    const isMobile = window.innerWidth < 640;
    const behavior = isMobile ? 'smooth' : 'smooth';
    const top = element.offsetTop - offset;

    window.scrollTo({
        top: top,
        behavior: behavior
    });
}

// Update scroll calls to use the new function
function scrollToOutput() {
    setTimeout(() => {
        scrollToElement(outputSection, 20);
    }, 500);
}
