// Set default active tab
document.getElementById("textTab").classList.add("active-tab");

// Handle tab switching
const tabs = document.querySelectorAll(".tab-btn");
const sections = document.querySelectorAll(".gif-section");

tabs.forEach(tab => {
    tab.addEventListener("click", () => {
        // Remove active class from all tabs
        tabs.forEach(t => t.classList.remove("active-tab"));
        // Add active class to clicked tab
        tab.classList.add("active-tab");

        // Hide all sections
        sections.forEach(section => section.classList.add("hidden"));

        // Show the corresponding section
        const sectionId = tab.id.replace("Tab", "Section");
        document.getElementById(sectionId).classList.remove("hidden");
    });
});

// Handle Generate Button
document.getElementById("generateBtn").addEventListener("click", async () => {
    const activeTab = document.querySelector(".active-tab")?.id;
    const loadingSection = document.getElementById("loadingSection");
    const outputSection = document.getElementById("outputSection");
    const gifPreview = document.getElementById("gifPreview");
    const downloadBtn = document.getElementById("downloadBtn");

    // Get progress step elements
    const step1 = document.getElementById("step1");
    const step2 = document.getElementById("step2");
    const step3 = document.getElementById("step3");

    // Reset output and show loading
    outputSection.classList.add("hidden");
    gifPreview.src = "";
    downloadBtn.href = "#";

    // Reset progress steps
    step1.className = "progress-step p-3 bg-gray-100 rounded-lg";
    step2.className = "progress-step p-3 bg-gray-100 rounded-lg opacity-50";
    step3.className = "progress-step p-3 bg-gray-100 rounded-lg opacity-50";

    try {
        let res, data;

        // Text to GIF
        if (activeTab === "textTab") {
            const prompt = document.getElementById("textPrompt").value.trim();
            if (!prompt) return alert("Please enter a prompt.");

            // Show loading section and update step 1
            loadingSection.classList.remove("hidden");
            step1.className = "progress-step p-3 active rounded-lg";
            step1.textContent = "⏳ Step 1: Generating 3 different images from your prompt...";

            res = await fetch("/api/text-to-gif", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ prompt }),
            });
        }

        // Image to GIF
        else if (activeTab === "imageTab") {
            const files = document.getElementById("imageUpload").files;
            if (files.length < 2 || files.length > 10) return alert("Upload 2–10 images.");

            // Show loading section and update step 1
            loadingSection.classList.remove("hidden");
            step1.className = "progress-step p-3 active rounded-lg";
            step1.textContent = "⏳ Step 1: Processing your images...";

            const formData = new FormData();
            for (let file of files) {
                formData.append("images", file);
            }

            // Update progress
            step1.className = "progress-step p-3 completed rounded-lg";
            step1.textContent = "✅ Step 1: Images processed successfully!";
            step2.className = "progress-step p-3 active rounded-lg";
            step2.textContent = "⏳ Step 2: Creating animated GIF...";

            res = await fetch("/api/image-to-gif", {
                method: "POST",
                body: formData,
            });

            // Update progress
            step2.className = "progress-step p-3 completed rounded-lg";
            step2.textContent = "✅ Step 2: GIF created successfully!";
            step3.className = "progress-step p-3 completed rounded-lg";
            step3.textContent = "✅ Step 3: GIF optimized and ready!";
        }

        // Video to GIF
        else if (activeTab === "videoTab") {
            const file = document.getElementById("videoUpload").files[0];
            if (!file) return alert("Please upload a video.");

            // Show loading section and update step 1
            loadingSection.classList.remove("hidden");
            step1.className = "progress-step p-3 active rounded-lg";
            step1.textContent = "⏳ Step 1: Processing your video...";

            const formData = new FormData();
            formData.append("video", file);

            // Update progress
            step1.className = "progress-step p-3 completed rounded-lg";
            step1.textContent = "✅ Step 1: Video processed successfully!";
            step2.className = "progress-step p-3 active rounded-lg";
            step2.textContent = "⏳ Step 2: Creating animated GIF...";

            res = await fetch("/api/video-to-gif", {
                method: "POST",
                body: formData,
            });

            // Update progress
            step2.className = "progress-step p-3 completed rounded-lg";
            step2.textContent = "✅ Step 2: GIF created successfully!";
            step3.className = "progress-step p-3 completed rounded-lg";
            step3.textContent = "✅ Step 3: GIF optimized and ready!";
        }

        data = await res.json();

        if (res.ok && data.gifUrl) {
            // Update progress steps based on the active tab
            if (activeTab === "textTab") {
                step1.className = "progress-step p-3 completed rounded-lg";
                step1.textContent = "✅ Step 1: 3 unique images generated successfully!";
                step2.className = "progress-step p-3 completed rounded-lg";
                step2.textContent = "✅ Step 2: Animated GIF created successfully!";
                step3.className = "progress-step p-3 completed rounded-lg";
                step3.textContent = "✅ Step 3: Multi-frame animation ready!";
            }

            // Hide loading section
            loadingSection.classList.add("hidden");

            // Show output section with the generated GIF
            gifPreview.src = data.gifUrl;
            downloadBtn.href = data.gifUrl;
            downloadBtn.setAttribute("download", "generated.gif");
            outputSection.classList.remove("hidden");

            // Add a small delay to ensure the GIF loads properly
            setTimeout(() => {
                // Scroll to the output section
                outputSection.scrollIntoView({ behavior: 'smooth' });
            }, 500);
        } else {
            // Hide loading section
            loadingSection.classList.add("hidden");

            alert("Failed to generate GIF. Please try again.");
            console.error("Error response:", data);

            // Reset progress steps
            if (activeTab === "textTab") {
                step1.className = "progress-step p-3 bg-red-100 rounded-lg";
                step1.textContent = "❌ Error generating images. Please try again.";
                step2.className = "progress-step p-3 bg-gray-100 rounded-lg opacity-50";
                step3.className = "progress-step p-3 bg-gray-100 rounded-lg opacity-50";
            }
        }
    } catch (err) {
        // Hide loading section
        loadingSection.classList.add("hidden");

        console.error("Error:", err);
        alert("Something went wrong while generating your GIF. Please try again.");

        // Reset progress steps for error state
        if (activeTab === "textTab") {
            step1.className = "progress-step p-3 bg-red-100 rounded-lg";
            step1.textContent = "❌ Error: " + (err.message || "Unknown error");
            step2.className = "progress-step p-3 bg-gray-100 rounded-lg opacity-50";
            step3.className = "progress-step p-3 bg-gray-100 rounded-lg opacity-50";
        }
    }
});
