// DOM Elements
const textPrompt = document.getElementById("textPrompt");
const generateBtn = document.getElementById("generateBtn");
const btnText = document.getElementById("btnText");
const btnLoader = document.getElementById("btnLoader");
const loadingSection = document.getElementById("loadingSection");
const outputSection = document.getElementById("outputSection");
const gifPreview = document.getElementById("gifPreview");
const downloadBtn = document.getElementById("downloadBtn");
const shareBtn = document.getElementById("shareBtn");
const newGifBtn = document.getElementById("newGifBtn");
const sampleBtn = document.getElementById("sampleBtn");
const errorSection = document.getElementById("errorSection");
const errorMessage = document.getElementById("errorMessage");
const retryBtn = document.getElementById("retryBtn");
const dismissErrorBtn = document.getElementById("dismissErrorBtn");

// Progress step elements
const step1 = document.getElementById("step1");
const step2 = document.getElementById("step2");
const step3 = document.getElementById("step3");

// Sample prompts for inspiration
const samplePrompts = [
    "A majestic dragon flying through clouds with lightning in the background",
    "A robot dancing in a neon-lit cyberpunk city at night",
    "Glowing butterflies flying around a magical enchanted forest",
    "A cute cat wearing sunglasses riding a skateboard",
    "Ocean waves crashing against rocks during a beautiful sunset",
    "A spaceship traveling through a colorful galaxy with stars",
    "A phoenix rising from flames with golden feathers",
    "A wizard casting spells with magical sparkles and energy",
    "Cherry blossoms falling in a peaceful Japanese garden",
    "A futuristic car racing through a neon tunnel"
];

// Add sample prompt functionality
function addSamplePrompt() {
    const randomPrompt = samplePrompts[Math.floor(Math.random() * samplePrompts.length)];
    textPrompt.value = randomPrompt;

    // Update character counter
    const event = new Event('input', { bubbles: true });
    textPrompt.dispatchEvent(event);

    // Focus and scroll into view on mobile
    textPrompt.focus();
    if (window.innerWidth < 640) {
        setTimeout(() => {
            textPrompt.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }, 100);
    }
}

// Add event listener for sample button
sampleBtn.addEventListener('click', function () {
    console.log('Sample button clicked');
    addSamplePrompt();
});

// Utility functions
function resetProgressSteps() {
    const isMobile = window.innerWidth < 640;
    const stepClass = isMobile ?
        "progress-step p-3 bg-gray-50 rounded-lg border-l-4 border-gray-300" :
        "progress-step p-4 bg-gray-50 rounded-xl border-l-4 border-gray-300";
    const iconSize = isMobile ? "text-lg" : "text-2xl";
    const textSize = isMobile ? "text-sm" : "text-base";

    step1.className = stepClass;
    step2.className = stepClass + " opacity-50";
    step3.className = stepClass + " opacity-50";

    step1.innerHTML = `<div class="flex items-center"><span class="${iconSize} mr-2 sm:mr-3">⏳</span><span class="${textSize}">Step 1: Generating unique images from your prompt...</span></div>`;
    step2.innerHTML = `<div class="flex items-center"><span class="${iconSize} mr-2 sm:mr-3">⌛</span><span class="${textSize}">Step 2: Creating smooth animated transitions...</span></div>`;
    step3.innerHTML = `<div class="flex items-center"><span class="${iconSize} mr-2 sm:mr-3">⌛</span><span class="${textSize}">Step 3: Optimizing and finalizing your GIF...</span></div>`;
}

function updateProgressStep(stepElement, status, text, icon) {
    const isMobile = window.innerWidth < 640;
    const stepClass = isMobile ?
        "progress-step p-3 bg-gray-50 rounded-lg border-l-4 border-gray-300" :
        "progress-step p-4 bg-gray-50 rounded-xl border-l-4 border-gray-300";
    const iconSize = isMobile ? "text-lg" : "text-2xl";
    const textSize = isMobile ? "text-sm" : "text-base";

    stepElement.className = `${stepClass} ${status}`;
    stepElement.innerHTML = `<div class="flex items-center"><span class="${iconSize} mr-2 sm:mr-3">${icon}</span><span class="${textSize}">${text}</span></div>`;
}

function setButtonLoading(loading) {
    generateBtn.disabled = loading;
    if (loading) {
        btnText.classList.add("hidden");
        btnLoader.classList.remove("hidden");
    } else {
        btnText.classList.remove("hidden");
        btnLoader.classList.add("hidden");
    }
}

// Error display functions
function showError(message, isServerError = false) {
    // Hide loading and output sections
    loadingSection.classList.add("hidden");
    outputSection.classList.add("hidden");
    setButtonLoading(false);

    // Determine error message based on type
    let displayMessage = message;
    if (isServerError) {
        if (message.includes("exceeded your monthly included credits") || message.includes("402")) {
            displayMessage = "🔑 Our AI service has reached its monthly limit. This is a temporary issue that will be resolved soon. Please try again later or contact support if this persists.";
        } else if (message.includes("Model is currently loading") || message.includes("503")) {
            displayMessage = "🔄 The AI model is starting up. This usually takes 10-20 seconds. Please wait a moment and try again.";
        } else if (message.includes("Rate limit exceeded") || message.includes("429")) {
            displayMessage = "⏳ You're generating images a bit too quickly! Please wait 10-15 seconds before trying again to avoid rate limits.";
        } else if (message.includes("API credits exhausted") || message.includes("402")) {
            displayMessage = "💳 The free API credits have been used up for today. Please try again tomorrow or consider upgrading to a premium plan.";
        } else if (message.includes("DeepAI API Error")) {
            displayMessage = "🤖 There's an issue with the AI image generation service. Please try again in a few moments.";
        } else if (message.includes("timeout") || message.includes("504")) {
            displayMessage = "⏱️ The AI service is taking longer than expected to respond. This might be due to high demand. Please try again in a few moments.";
        } else if (message.includes("network") || message.includes("fetch")) {
            displayMessage = "🌐 There seems to be a connection issue. Please check your internet connection and try again.";
        } else {
            displayMessage = "🔧 We're experiencing some technical difficulties on our end. Our team has been notified and is working to fix this. Please try again in a few minutes.";
        }
    }

    // Show error message
    errorMessage.textContent = displayMessage;
    errorSection.classList.remove("hidden");

    // Scroll to error message
    setTimeout(() => {
        errorSection.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }, 100);
}

function hideError() {
    errorSection.classList.add("hidden");
}

function validatePrompt(prompt) {
    if (!prompt) {
        showError("📝 Please enter a description for your GIF to get started!");
        textPrompt.focus();
        return false;
    }

    if (prompt.length < 10) {
        showError("✏️ Please provide a more detailed description (at least 10 characters) to help our AI create a better GIF.");
        textPrompt.focus();
        return false;
    }

    if (prompt.length > 500) {
        showError("📏 Please keep your description under 500 characters for optimal results.");
        textPrompt.focus();
        return false;
    }

    return true;
}

// Handle Generate Button Click
async function handleGenerate() {
    const prompt = textPrompt.value.trim();

    // Validate input
    if (!validatePrompt(prompt)) return;

    // Reset UI state
    outputSection.classList.add("hidden");
    errorSection.classList.add("hidden");
    gifPreview.src = "";
    downloadBtn.href = "#";
    resetProgressSteps();
    setButtonLoading(true);

    try {
        // Show loading section
        loadingSection.classList.remove("hidden");

        // Update step 1
        updateProgressStep(step1, "active", "Generating unique images from your prompt...", "⏳");

        // Prepare request data
        const requestData = {
            prompt: prompt
        };

        // Make API request
        const res = await fetch("/api/text-to-gif", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(requestData),
        });

        const data = await res.json();

        if (res.ok && data.gifUrl) {
            // Update progress steps for success
            updateProgressStep(step1, "completed", "3 unique images generated successfully!", "✅");
            updateProgressStep(step2, "completed", "Animated GIF created successfully!", "✅");
            updateProgressStep(step3, "completed", "Multi-frame animation ready!", "✅");

            // Hide loading section
            loadingSection.classList.add("hidden");
            setButtonLoading(false);

            // Show output section with the generated GIF
            gifPreview.src = data.gifUrl;
            downloadBtn.href = data.gifUrl;
            downloadBtn.setAttribute("download", `gifcraft-${Date.now()}.gif`);
            outputSection.classList.remove("hidden");

            // Scroll to the output section
            scrollToOutput();

        } else {
            // Handle API error
            const errorMsg = data.details || data.error || "Failed to generate GIF";
            console.error("API Error:", data);

            // Show user-friendly error message
            showError(errorMsg, true);

            // Update progress steps for error
            updateProgressStep(step1, "error", "Error generating images. Please try again.", "❌");
        }

    } catch (err) {
        // Handle network/other errors
        console.error("Network Error:", err);

        // Show user-friendly error message
        showError(err.message || "Unknown error", true);

        // Update progress steps for error
        updateProgressStep(step1, "error", `Error: ${err.message || "Unknown error"}`, "❌");
    }
}

// Add event listeners for generate button
generateBtn.addEventListener("click", handleGenerate);

// Handle Share Button
function handleShare() {
    if (navigator.share && gifPreview.src) {
        navigator.share({
            title: 'Check out my AI-generated GIF!',
            text: 'I created this amazing GIF using GIFCraft - AI Powered GIF Generator',
            url: window.location.href
        }).catch(err => {
            console.log('Error sharing:', err);
            copyToClipboard(window.location.href);
        });
    } else {
        copyToClipboard(window.location.href);
    }
}

shareBtn.addEventListener("click", handleShare);

// Handle New GIF Button
function handleNewGif() {
    // Reset form
    textPrompt.value = "";

    // Reset character counter
    charCounter.textContent = '0/500';
    charCounter.className = 'text-xs text-gray-400';

    // Reset textarea height
    textPrompt.style.height = 'auto';

    // Hide output and error sections
    outputSection.classList.add("hidden");
    errorSection.classList.add("hidden");

    // Focus on prompt input
    textPrompt.focus();

    // Scroll to top
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

newGifBtn.addEventListener("click", handleNewGif);

// Error section event listeners
retryBtn.addEventListener("click", function () {
    hideError();
    handleGenerate();
});

dismissErrorBtn.addEventListener("click", function () {
    hideError();
});

// Utility function to copy to clipboard
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        alert('Link copied to clipboard!');
    }).catch(() => {
        alert('Unable to copy link. Please copy manually: ' + text);
    });
}

// Add keyboard shortcuts
document.addEventListener('keydown', (e) => {
    // Ctrl/Cmd + Enter to generate
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        e.preventDefault();
        if (!generateBtn.disabled) {
            generateBtn.click();
        }
    }

    // Escape to cancel/reset
    if (e.key === 'Escape') {
        if (!loadingSection.classList.contains('hidden')) {
            // Could add cancel functionality here
        } else if (!outputSection.classList.contains('hidden')) {
            newGifBtn.click();
        }
    }
});

// Character counter and auto-resize functionality
const charCounter = document.getElementById('charCounter');

textPrompt.addEventListener('input', function () {
    const length = this.value.length;
    const maxLength = 500;

    // Update character counter
    charCounter.textContent = `${length}/${maxLength}`;

    // Change color based on length
    if (length > maxLength * 0.9) {
        charCounter.className = 'text-xs text-red-500';
    } else if (length > maxLength * 0.7) {
        charCounter.className = 'text-xs text-yellow-500';
    } else {
        charCounter.className = 'text-xs text-gray-400';
    }

    // Prevent exceeding max length
    if (length > maxLength) {
        this.value = this.value.substring(0, maxLength);
        charCounter.textContent = `${maxLength}/${maxLength}`;
        charCounter.className = 'text-xs text-red-500';
    }

    // Auto-resize textarea
    this.style.height = 'auto';
    this.style.height = this.scrollHeight + 'px';
});

// Initialize character counter
charCounter.textContent = '0/500';

// Mobile-specific improvements
function handleMobileResize() {
    // Reset progress steps on orientation change
    if (!loadingSection.classList.contains('hidden')) {
        resetProgressSteps();
    }
}

// Handle orientation change and resize
window.addEventListener('orientationchange', () => {
    setTimeout(handleMobileResize, 100);
});

window.addEventListener('resize', handleMobileResize);

// Prevent double-tap zoom on specific elements only
document.addEventListener('touchend', function (e) {
    // Only prevent double-tap zoom on non-interactive elements
    if (!e.target.closest('button, a, input, textarea')) {
        const now = Date.now();
        if (now - (window.lastTouchEnd || 0) <= 300) {
            e.preventDefault();
        }
        window.lastTouchEnd = now;
    }
}, false);

// Better mobile scroll behavior
function scrollToElement(element, offset = 20) {
    const isMobile = window.innerWidth < 640;
    const behavior = isMobile ? 'smooth' : 'smooth';
    const top = element.offsetTop - offset;

    window.scrollTo({
        top: top,
        behavior: behavior
    });
}

// Update scroll calls to use the new function
function scrollToOutput() {
    setTimeout(() => {
        scrollToElement(outputSection, 20);
    }, 500);
}
