// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`All align combinations for contain > horizontal contain aligned to CENTER BOTTOM 1`] = `
Visualization:

      
      
▴▴▴▸▸▸
▴▴▴▸▸▸
▾▾▾◆◆◆
▾▾▾◆◆◆

Data:

00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰
FF-00-00ᶠᶠ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
FF-00-00ᶠᶠ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
`;

exports[`All align combinations for contain > horizontal contain aligned to CENTER MIDDLE 1`] = `
Visualization:

      
▴▴▴▸▸▸
▴▴▴▸▸▸
▾▾▾◆◆◆
▾▾▾◆◆◆
      

Data:

00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰
FF-00-00ᶠᶠ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
FF-00-00ᶠᶠ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰
`;

exports[`All align combinations for contain > horizontal contain aligned to CENTER TOP 1`] = `
Visualization:

▴▴▴▸▸▸
▴▴▴▸▸▸
▾▾▾◆◆◆
▾▾▾◆◆◆
      
      

Data:

FF-00-00ᶠᶠ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
FF-00-00ᶠᶠ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰
`;

exports[`All align combinations for contain > horizontal contain aligned to LEFT BOTTOM 1`] = `
Visualization:

      
      
▴▴▴▸▸▸
▴▴▴▸▸▸
▾▾▾◆◆◆
▾▾▾◆◆◆

Data:

00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰
FF-00-00ᶠᶠ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
FF-00-00ᶠᶠ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
`;

exports[`All align combinations for contain > horizontal contain aligned to LEFT MIDDLE 1`] = `
Visualization:

      
▴▴▴▸▸▸
▴▴▴▸▸▸
▾▾▾◆◆◆
▾▾▾◆◆◆
      

Data:

00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰
FF-00-00ᶠᶠ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
FF-00-00ᶠᶠ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰
`;

exports[`All align combinations for contain > horizontal contain aligned to LEFT TOP 1`] = `
Visualization:

▴▴▴▸▸▸
▴▴▴▸▸▸
▾▾▾◆◆◆
▾▾▾◆◆◆
      
      

Data:

FF-00-00ᶠᶠ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
FF-00-00ᶠᶠ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰
`;

exports[`All align combinations for contain > horizontal contain aligned to RIGHT BOTTOM 1`] = `
Visualization:

      
      
▴▴▴▸▸▸
▴▴▴▸▸▸
▾▾▾◆◆◆
▾▾▾◆◆◆

Data:

00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰
FF-00-00ᶠᶠ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
FF-00-00ᶠᶠ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
`;

exports[`All align combinations for contain > horizontal contain aligned to RIGHT MIDDLE 1`] = `
Visualization:

      
▴▴▴▸▸▸
▴▴▴▸▸▸
▾▾▾◆◆◆
▾▾▾◆◆◆
      

Data:

00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰
FF-00-00ᶠᶠ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
FF-00-00ᶠᶠ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰
`;

exports[`All align combinations for contain > horizontal contain aligned to RIGHT TOP 1`] = `
Visualization:

▴▴▴▸▸▸
▴▴▴▸▸▸
▾▾▾◆◆◆
▾▾▾◆◆◆
      
      

Data:

FF-00-00ᶠᶠ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
FF-00-00ᶠᶠ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
00-00-FFᶠᶠ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰
`;

exports[`All align combinations for contain > vertical contain aligned to CENTER BOTTOM 1`] = `
Visualization:

 ▴▴▸▸ 
 ▴▴▸▸ 
 ▴▴▸▸ 
 ▾▾◆◆ 
 ▾▾◆◆ 
 ▾▾◆◆ 

Data:

00-00-00⁰⁰ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-00-00⁰⁰
00-00-00⁰⁰ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-00-00⁰⁰
00-00-00⁰⁰ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰
`;

exports[`All align combinations for contain > vertical contain aligned to CENTER MIDDLE 1`] = `
Visualization:

 ▴▴▸▸ 
 ▴▴▸▸ 
 ▴▴▸▸ 
 ▾▾◆◆ 
 ▾▾◆◆ 
 ▾▾◆◆ 

Data:

00-00-00⁰⁰ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-00-00⁰⁰
00-00-00⁰⁰ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-00-00⁰⁰
00-00-00⁰⁰ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰
`;

exports[`All align combinations for contain > vertical contain aligned to CENTER TOP 1`] = `
Visualization:

 ▴▴▸▸ 
 ▴▴▸▸ 
 ▴▴▸▸ 
 ▾▾◆◆ 
 ▾▾◆◆ 
 ▾▾◆◆ 

Data:

00-00-00⁰⁰ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-00-00⁰⁰
00-00-00⁰⁰ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-00-00⁰⁰
00-00-00⁰⁰ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰
`;

exports[`All align combinations for contain > vertical contain aligned to LEFT BOTTOM 1`] = `
Visualization:

▴▴▸▸  
▴▴▸▸  
▴▴▸▸  
▾▾◆◆  
▾▾◆◆  
▾▾◆◆  

Data:

FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰
FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰
FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰
`;

exports[`All align combinations for contain > vertical contain aligned to LEFT MIDDLE 1`] = `
Visualization:

▴▴▸▸  
▴▴▸▸  
▴▴▸▸  
▾▾◆◆  
▾▾◆◆  
▾▾◆◆  

Data:

FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰
FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰
FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰
`;

exports[`All align combinations for contain > vertical contain aligned to LEFT TOP 1`] = `
Visualization:

▴▴▸▸  
▴▴▸▸  
▴▴▸▸  
▾▾◆◆  
▾▾◆◆  
▾▾◆◆  

Data:

FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰
FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰
FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰
`;

exports[`All align combinations for contain > vertical contain aligned to RIGHT BOTTOM 1`] = `
Visualization:

  ▴▴▸▸
  ▴▴▸▸
  ▴▴▸▸
  ▾▾◆◆
  ▾▾◆◆
  ▾▾◆◆

Data:

00-00-00⁰⁰ 00-00-00⁰⁰ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
00-00-00⁰⁰ 00-00-00⁰⁰ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
00-00-00⁰⁰ 00-00-00⁰⁰ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
`;

exports[`All align combinations for contain > vertical contain aligned to RIGHT MIDDLE 1`] = `
Visualization:

  ▴▴▸▸
  ▴▴▸▸
  ▴▴▸▸
  ▾▾◆◆
  ▾▾◆◆
  ▾▾◆◆

Data:

00-00-00⁰⁰ 00-00-00⁰⁰ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
00-00-00⁰⁰ 00-00-00⁰⁰ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
00-00-00⁰⁰ 00-00-00⁰⁰ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
`;

exports[`All align combinations for contain > vertical contain aligned to RIGHT TOP 1`] = `
Visualization:

  ▴▴▸▸
  ▴▴▸▸
  ▴▴▸▸
  ▾▾◆◆
  ▾▾◆◆
  ▾▾◆◆

Data:

00-00-00⁰⁰ 00-00-00⁰⁰ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
00-00-00⁰⁰ 00-00-00⁰⁰ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
00-00-00⁰⁰ 00-00-00⁰⁰ FF-00-00ᶠᶠ FF-00-00ᶠᶠ 00-FF-00ᶠᶠ 00-FF-00ᶠᶠ
00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-FFᶠᶠ 00-00-FFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ
`;
