// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Autocrop > ignore sides east 1`] = `
Visualization:

  ◆◆    
 ◆▦▦◆   
◆▦▦▦▦◆  
 ◆▦▦◆   
  ◆◆    

Data:

00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰
FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰
`;

exports[`Autocrop > ignore sides north 1`] = `
Visualization:

      
  ◆◆  
 ◆▦▦◆ 
◆▦▦▦▦◆
 ◆▦▦◆ 
  ◆◆  

Data:

00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰
FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ
00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰
`;

exports[`Autocrop > ignore sides south and west 1`] = `
Visualization:

    ◆◆  
   ◆▦▦◆ 
  ◆▦▦▦▦◆
   ◆▦▦◆ 
    ◆◆  
        

Data:

00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ
00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰
`;

exports[`Autocrop > image border with small variation 1`] = `
Visualization:

323232323232
232323232323
32   ◆◆   32
23  ◆▦▦◆  23
32 ◆▦▦▦▦◆ 32
23  ◆▦▦◆  23
32   ◆◆   32
232323232323
323232323232

Data:

33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ
33-33-33ᶠᶠ 22-22-22ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 33-33-33ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ 33-33-33ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 22-22-22ᶠᶠ 33-33-33ᶠᶠ
33-33-33ᶠᶠ 22-22-22ᶠᶠ 00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 33-33-33ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ 33-33-33ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 22-22-22ᶠᶠ 33-33-33ᶠᶠ
33-33-33ᶠᶠ 22-22-22ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 33-33-33ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ
33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ
`;

exports[`Autocrop > image border with small variation 2`] = `
Visualization:

   ◆◆   
  ◆▦▦◆  
 ◆▦▦▦▦◆ 
  ◆▦▦◆  
   ◆◆   

Data:

00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰
`;

exports[`Autocrop > image border with small variation configured by options 1`] = `
Visualization:

323232323232
232323232323
32   ◆◆   32
23  ◆▦▦◆  23
32 ◆▦▦▦▦◆ 32
23  ◆▦▦◆  23
32   ◆◆   32
232323232323
323232323232

Data:

33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ
33-33-33ᶠᶠ 22-22-22ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 33-33-33ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ 33-33-33ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 22-22-22ᶠᶠ 33-33-33ᶠᶠ
33-33-33ᶠᶠ 22-22-22ᶠᶠ 00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 33-33-33ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ 33-33-33ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 22-22-22ᶠᶠ 33-33-33ᶠᶠ
33-33-33ᶠᶠ 22-22-22ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 33-33-33ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ
33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ
`;

exports[`Autocrop > image border with small variation configured by options 2`] = `
Visualization:

   ◆◆   
  ◆▦▦◆  
 ◆▦▦▦▦◆ 
  ◆▦▦◆  
   ◆◆   

Data:

00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰
`;

exports[`Autocrop > image not cropped given an out of bounds "leaveBorder" value  1`] = `
Visualization:

323232323232
232323232323
32   ◆◆   32
23  ◆▦▦◆  23
32 ◆▦▦▦▦◆ 32
23  ◆▦▦◆  23
32   ◆◆   32
232323232323
323232323232

Data:

33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ
33-33-33ᶠᶠ 22-22-22ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 33-33-33ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ 33-33-33ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 22-22-22ᶠᶠ 33-33-33ᶠᶠ
33-33-33ᶠᶠ 22-22-22ᶠᶠ 00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 33-33-33ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ 33-33-33ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 22-22-22ᶠᶠ 33-33-33ᶠᶠ
33-33-33ᶠᶠ 22-22-22ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 33-33-33ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ
33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ
`;

exports[`Autocrop > image with one color border 1`] = `
Visualization:

   ◆◆   
  ◆▦▦◆  
 ◆▦▦▦▦◆ 
  ◆▦▦◆  
   ◆◆   

Data:

00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰
`;

exports[`Autocrop > image with opaque surround color 1`] = `
Visualization:

▥▥◆◆▥▥
▥◆▦▦◆▥
◆▦▦▦▦◆
▥◆▦▦◆▥
▥▥◆◆▥▥

Data:

BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ
BF-BF-BFᶠᶠ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ BF-BF-BFᶠᶠ
FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ
BF-BF-BFᶠᶠ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ BF-BF-BFᶠᶠ
BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ
`;

exports[`Autocrop > image with symmetric border configured by options 1`] = `
Visualization:

   ◆◆   ▥▥
  ◆▦▦◆  ▥▥
 ◆▦▦▦▦◆ ▥▥
  ◆▦▦◆  ▥▥
   ◆◆   ▥▥
▥▥▥▥▥▥▥▥▥▥

Data:

00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ
00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ
00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ
00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ
00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ
BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ
`;

exports[`Autocrop > image with top and bottom frame and leaveBorder 1`] = `
Visualization:

▥▥▥▥▥▥▥▥
▥▥▥▥▥▥▥▥
   ◆◆   
  ◆▦▦◆  
 ◆▦▦▦▦◆ 
  ◆▦▦◆  
   ◆◆   
▥▥▥▥▥▥▥▥
▥▥▥▥▥▥▥▥

Data:

BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ
BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ
00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰
BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ
BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ
`;

exports[`Autocrop > image with transparent surround color 1`] = `
Visualization:

  ◆◆  
 ◆▦▦◆ 
◆▦▦▦▦◆
 ◆▦▦◆ 
  ◆◆  

Data:

00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰
FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ
00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰
`;

exports[`Autocrop > image without frame 1`] = `
Visualization:

   ◆◆   
  ◆▦▦◆  
 ◆▦▦▦▦◆ 
  ◆▦▦◆  
   ◆◆   

Data:

00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰
`;

exports[`Autocrop > image without frame and with some border left 1`] = `
Visualization:

3232323232
2   ◆◆   3
3  ◆▦▦◆  2
2 ◆▦▦▦▦◆ 3
3  ◆▦▦◆  2
2   ◆◆   3
3232323232

Data:

33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 33-33-33ᶠᶠ
33-33-33ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 22-22-22ᶠᶠ
22-22-22ᶠᶠ 00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 33-33-33ᶠᶠ
33-33-33ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 22-22-22ᶠᶠ
22-22-22ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ 33-33-33ᶠᶠ
33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ 33-33-33ᶠᶠ 22-22-22ᶠᶠ
`;

exports[`Autocrop > image without frame and with symmetric border configured by options 1`] = `
Visualization:

   ◆◆   ▥▥
  ◆▦▦◆  ▥▥
 ◆▦▦▦▦◆ ▥▥
  ◆▦▦◆  ▥▥
   ◆◆   ▥▥
▥▥▥▥▥▥▥▥▥▥
▥▥▥▥▥▥▥▥▥▥

Data:

00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ
00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ
00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ
00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ
00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ
BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ
BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ BF-BF-BFᶠᶠ
`;

exports[`Autocrop > image without frame configured by options 1`] = `
Visualization:

   ◆◆   
  ◆▦▦◆  
 ◆▦▦▦▦◆ 
  ◆▦▦◆  
   ◆◆   

Data:

00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ 80-80-80ᶠᶠ 80-80-80ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰
00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰ FF-FF-00ᶠᶠ FF-FF-00ᶠᶠ 00-00-00⁰⁰ 00-00-00⁰⁰ 00-00-00⁰⁰
`;
