// utils/multiFrameGifCreator.js
const fs = require('fs');
const path = require('path');
const fetch = require('node-fetch');
const { createCanvas, loadImage } = require('canvas');
const GIFEncoder = require('gifencoder');

/**
 * Sleep function for adding delays
 * @param {number} ms - Milliseconds to sleep
 */
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Generate a single image with retry logic
 * @param {string} prompt - The text prompt
 * @param {number} imageIndex - Index of the image (for logging)
 * @param {number} maxRetries - Maximum number of retries
 * @returns {Promise<string|null>} - Path to the generated image or null if failed
 */
async function generateSingleImage(prompt, imageIndex, maxRetries = 3) {
  const imagesDir = path.join(__dirname, '../temp/images');

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`Generating image ${imageIndex} (attempt ${attempt}/${maxRetries}) with prompt: "${prompt}"`);

      const response = await fetch('https://replicate-api-proxy.glitch.me/stable-diffusion', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ prompt })
      });

      if (!response.ok) {
        const errText = await response.text();

        // Handle specific error cases for Replicate proxy
        if (response.status === 429) {
          throw new Error(`Rate limit exceeded. Please wait a moment before trying again. (Status: ${response.status})`);
        } else if (response.status === 503) {
          throw new Error(`Service temporarily unavailable. Please try again in a moment. (Status: ${response.status})`);
        } else {
          throw new Error(`Replicate API Error (${response.status}): ${errText}`);
        }
      }

      // Replicate proxy returns direct image data as buffer
      const buffer = await response.buffer();
      const imagePath = path.join(imagesDir, `image${imageIndex}.png`);
      fs.writeFileSync(imagePath, buffer);
      console.log(`Image ${imageIndex} saved to ${imagePath}`);
      return imagePath;

    } catch (error) {
      console.error(`Error generating image ${imageIndex} (attempt ${attempt}):`, error.message);

      if (attempt < maxRetries) {
        // Exponential backoff: wait longer between retries
        const waitTime = Math.pow(2, attempt) * 1000; // 2s, 4s, 8s...
        console.log(`Waiting ${waitTime}ms before retry...`);
        await sleep(waitTime);
      } else {
        console.error(`Failed to generate image ${imageIndex} after ${maxRetries} attempts`);
        return null;
      }
    }
  }
  return null;
}

/**
 * Generate a GIF from a text prompt
 * @param {string} prompt - The text prompt
 * @returns {Promise<string>} - URL of the generated GIF
 */
async function generateTextGif(prompt) {
  console.log('Starting multi-frame GIF generation...');

  // Ensure directories exist
  const tempDir = path.join(__dirname, '../temp');
  const imagesDir = path.join(tempDir, 'images');
  const outputDir = path.join(__dirname, '../public/generated');
  fs.mkdirSync(tempDir, { recursive: true });
  fs.mkdirSync(imagesDir, { recursive: true });
  fs.mkdirSync(outputDir, { recursive: true });

  try {
    // Generate 3 DIFFERENT images from the prompt (reduced from 5 to avoid rate limiting)
    console.log(`Generating 3 different images for prompt: "${prompt}"`);
    const imagePaths = [];

    // Create 3 variations of the prompt to get different images
    const promptVariations = [
      prompt,
      `${prompt}, detailed view`,
      `${prompt}, different perspective`
    ];

    // Generate images for each prompt variation with delays to avoid rate limiting
    for (let i = 0; i < promptVariations.length; i++) {
      // Add delay between requests (except for the first one)
      if (i > 0) {
        console.log('Waiting 2 seconds before next request to avoid rate limiting...');
        await sleep(2000);
      }

      const imagePath = await generateSingleImage(promptVariations[i], i + 1);
      if (imagePath) {
        imagePaths.push(imagePath);
      }
    }

    // Check if we have at least one image to create a GIF
    if (imagePaths.length === 0) {
      throw new Error('Failed to generate any images');
    }

    console.log(`Successfully generated ${imagePaths.length} out of ${promptVariations.length} images`);

    // Create a GIF from the generated images
    const timestamp = Date.now();
    const gifFilename = `output_${timestamp}.gif`;
    const gifPath = path.join(outputDir, gifFilename);
    const gifUrl = `/generated/${gifFilename}`;

    console.log('Creating animated GIF from multiple frames...');

    // Load all images with canvas
    const loadedImages = await Promise.all(
      imagePaths.map(async (imgPath) => await loadImage(imgPath))
    );

    // Get dimensions
    const width = 512;
    const height = 512;

    // Create canvas
    const canvas = createCanvas(width, height);
    const ctx = canvas.getContext('2d');

    // Create GIF encoder
    const encoder = new GIFEncoder(width, height);
    const stream = fs.createWriteStream(gifPath);
    encoder.createReadStream().pipe(stream);

    // Start encoding
    encoder.start();
    encoder.setRepeat(0);  // 0 = loop forever
    encoder.setDelay(500);  // 500ms delay between frames
    encoder.setQuality(10); // Lower quality for smaller file size

    // Add each frame
    for (const img of loadedImages) {
      // Clear canvas
      ctx.fillStyle = '#FFFFFF';
      ctx.fillRect(0, 0, width, height);

      // Calculate dimensions to maintain aspect ratio
      const aspectRatio = img.width / img.height;
      let drawWidth, drawHeight, offsetX, offsetY;

      if (aspectRatio > 1) {
        // Image is wider than tall
        drawWidth = width;
        drawHeight = width / aspectRatio;
        offsetX = 0;
        offsetY = (height - drawHeight) / 2;
      } else {
        // Image is taller than wide
        drawHeight = height;
        drawWidth = height * aspectRatio;
        offsetX = (width - drawWidth) / 2;
        offsetY = 0;
      }

      // Draw image centered on canvas
      ctx.drawImage(img, offsetX, offsetY, drawWidth, drawHeight);

      // Add frame to GIF
      encoder.addFrame(ctx);
      console.log('Added frame to GIF');
    }

    // Finish encoding
    encoder.finish();
    console.log('Finished encoding GIF');

    // Return a promise that resolves when the file is written
    return new Promise((resolve, reject) => {
      stream.on('finish', () => {
        console.log(`Multi-frame GIF created successfully at ${gifPath}`);
        resolve(gifUrl);
      });

      stream.on('error', (err) => {
        console.error('Error writing GIF file:', err);
        reject(err);
      });
    });
  } catch (error) {
    console.error('Error in GIF generation:', error);
    throw error;
  }
}

module.exports = { generateTextGif };
