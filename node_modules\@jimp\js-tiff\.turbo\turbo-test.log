

> @jimp/js-tiff@1.1.2 test /Users/<USER>/Documents/jimp/plugins/js-tiff
> vitest "--watch=false"


[7m[1m[36m RUN [39m[22m[27m [36mv2.0.5[39m [90m/Users/<USER>/Documents/jimp/plugins/js-tiff[39m

[?25l [90m·[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
   [90m·[39m TIFF[2m (2)[22m
     [90m·[39m load TIFF
     [90m·[39m export TIFF
[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
   [32m✓[39m TIFF[2m (2)[22m
     [32m✓[39m load TIFF
     [32m✓[39m export TIFF

[2m Test Files [22m [1m[32m1 passed[39m[22m[90m (1)[39m
[2m      Tests [22m [1m[32m2 passed[39m[22m[90m (2)[39m
[2m   Start at [22m 01:33:35
[2m   Duration [22m 2.59s[2m (transform 416ms, setup 0ms, collect 612ms, tests 187ms, environment 0ms, prepare 254ms)[22m

[?25h[?25h
