{"version": 3, "file": "index.js", "names": ["GIF", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BitmapImage", "GifCodec", "MIME_TYPE", "mime", "constants", "MIME_GIF", "decoders", "data", "gifObj", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gifData", "<PERSON><PERSON><PERSON>", "alloc", "width", "height", "decodeAndBlitFrameRGBA", "encoders", "bitmap", "quantizeDekker", "newFrame", "gifCodec", "encodeGif", "then", "newGif", "buffer"], "sources": ["../src/index.js"], "sourcesContent": ["import GIF from \"omggif\";\nimport { GifUtil, GifFrame, BitmapImage, GifCodec } from \"gifwrap\";\n\nconst MIME_TYPE = \"image/gif\";\n\nexport default () => ({\n  mime: { [MIME_TYPE]: [\"gif\"] },\n\n  constants: {\n    MIME_GIF: MIME_TYPE,\n  },\n\n  decoders: {\n    [MIME_TYPE]: (data) => {\n      const gifObj = new GIF.GifReader(data);\n      const gifData = Buffer.alloc(gifObj.width * gifObj.height * 4);\n\n      gifObj.decodeAndBlitFrameRGBA(0, gifData);\n\n      return {\n        data: gifData,\n        width: gifObj.width,\n        height: gifObj.height,\n      };\n    },\n  },\n\n  encoders: {\n    [MIME_TYPE]: (data) => {\n      const bitmap = new BitmapImage(data.bitmap);\n      GifUtil.quantizeDekker(bitmap, 256);\n      const newFrame = new GifFrame(bitmap);\n      const gifCodec = new GifCodec();\n      return gifCodec.encodeGif([newFrame], {}).then((newGif) => {\n        return newGif.buffer;\n      });\n    },\n  },\n});\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,QAAQ;AACxB,SAASC,OAAO,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,QAAQ,QAAQ,SAAS;AAElE,MAAMC,SAAS,GAAG,WAAW;AAE7B,gBAAe,OAAO;EACpBC,IAAI,EAAE;IAAE,CAACD,SAAS,GAAG,CAAC,KAAK;EAAE,CAAC;EAE9BE,SAAS,EAAE;IACTC,QAAQ,EAAEH;EACZ,CAAC;EAEDI,QAAQ,EAAE;IACR,CAACJ,SAAS,GAAIK,IAAI,IAAK;MACrB,MAAMC,MAAM,GAAG,IAAIX,GAAG,CAACY,SAAS,CAACF,IAAI,CAAC;MACtC,MAAMG,OAAO,GAAGC,MAAM,CAACC,KAAK,CAACJ,MAAM,CAACK,KAAK,GAAGL,MAAM,CAACM,MAAM,GAAG,CAAC,CAAC;MAE9DN,MAAM,CAACO,sBAAsB,CAAC,CAAC,EAAEL,OAAO,CAAC;MAEzC,OAAO;QACLH,IAAI,EAAEG,OAAO;QACbG,KAAK,EAAEL,MAAM,CAACK,KAAK;QACnBC,MAAM,EAAEN,MAAM,CAACM;MACjB,CAAC;IACH;EACF,CAAC;EAEDE,QAAQ,EAAE;IACR,CAACd,SAAS,GAAIK,IAAI,IAAK;MACrB,MAAMU,MAAM,GAAG,IAAIjB,WAAW,CAACO,IAAI,CAACU,MAAM,CAAC;MAC3CnB,OAAO,CAACoB,cAAc,CAACD,MAAM,EAAE,GAAG,CAAC;MACnC,MAAME,QAAQ,GAAG,IAAIpB,QAAQ,CAACkB,MAAM,CAAC;MACrC,MAAMG,QAAQ,GAAG,IAAInB,QAAQ,EAAE;MAC/B,OAAOmB,QAAQ,CAACC,SAAS,CAAC,CAACF,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAACG,IAAI,CAAEC,MAAM,IAAK;QACzD,OAAOA,MAAM,CAACC,MAAM;MACtB,CAAC,CAAC;IACJ;EACF;AACF,CAAC,CAAC"}