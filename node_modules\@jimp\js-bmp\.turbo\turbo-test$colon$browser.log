
> @jimp/js-bmp@1.1.1 test:browser /Users/<USER>/Documents/jimp/plugins/js-bmp
> vitest --config vitest.config.browser.mjs "--watch=false" "--u"


[7m[1m[36m RUN [39m[22m[27m [36mv1.4.0[39m [90m/Users/<USER>/Documents/jimp/plugins/js-bmp[39m
[2m[32m      Browser runner started at http://localhost:5175/[39m[22m

[?25l [90m·[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
   [90m·[39m BMP[2m (2)[22m
     [90m·[39m load BMP
     [90m·[39m export BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [33m❯[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
   [33m❯[39m BMP[2m (2)[22m
     [33m⠙[39m load BMP
     [90m·[39m export BMP
[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
   [32m✓[39m BMP[2m (2)[22m
     [32m✓[39m load BMP
     [32m✓[39m export BMP
[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (2)[22m
   [32m✓[39m BMP[2m (2)[22m
     [32m✓[39m load BMP
     [32m✓[39m export BMP

[2m Test Files [22m [1m[32m1 passed[39m[22m[90m (1)[39m
[2m      Tests [22m [1m[32m2 passed[39m[22m[90m (2)[39m
[2m   Start at [22m 00:42:10
[2m   Duration [22m 6.37s[2m (transform 0ms, setup 0ms, collect 226ms, tests 55ms, environment 0ms, prepare 0ms)[22m

[?25h[?25h
