// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Fisheye > should create fisheye lens to image 1`] = `
Visualization:

■■■1221■■■
■22111222■
■22■■■■121
11■■■■■112
21■■■■■■12
21■■■■■■12
12■■■■■■12
■211■■■222
■22111122■
■■122222■■

Data:

00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 11-11-11ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 11-11-11ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ
00-00-00ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 11-11-11ᶠᶠ 11-11-11ᶠᶠ 11-11-11ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 00-00-00ᶠᶠ
00-00-00ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 11-11-11ᶠᶠ 22-22-22ᶠᶠ 11-11-11ᶠᶠ
11-11-11ᶠᶠ 11-11-11ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 11-11-11ᶠᶠ 11-11-11ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ 11-11-11ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 11-11-11ᶠᶠ 22-22-22ᶠᶠ
22-22-22ᶠᶠ 11-11-11ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 11-11-11ᶠᶠ 22-22-22ᶠᶠ
11-11-11ᶠᶠ 22-22-22ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 11-11-11ᶠᶠ 22-22-22ᶠᶠ
00-00-00ᶠᶠ 22-22-22ᶠᶠ 11-11-11ᶠᶠ 11-11-11ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ
00-00-00ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 11-11-11ᶠᶠ 11-11-11ᶠᶠ 11-11-11ᶠᶠ 11-11-11ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 00-00-00ᶠᶠ
00-00-00ᶠᶠ 00-00-00ᶠᶠ 11-11-11ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 22-22-22ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ
`;

exports[`Fisheye > should create fisheye lens to image with radius 1`] = `
Visualization:

■■■■■■■■■■
■■■■■■■■■■
■■■■■■■■■■
■■■11111■■
■■111111■■
■■111111■■
■■■■111■■■
■■■■■■■■■■
■■■■■■■■■■
■■■■■■■■■■

Data:

00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ
00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ
00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ
00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 11-11-11ᶠᶠ 11-11-11ᶠᶠ 11-11-11ᶠᶠ 11-11-11ᶠᶠ 11-11-11ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ
00-00-00ᶠᶠ 00-00-00ᶠᶠ 11-11-11ᶠᶠ 11-11-11ᶠᶠ 11-11-11ᶠᶠ 11-11-11ᶠᶠ 11-11-11ᶠᶠ 11-11-11ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ
00-00-00ᶠᶠ 00-00-00ᶠᶠ 11-11-11ᶠᶠ 11-11-11ᶠᶠ 11-11-11ᶠᶠ 11-11-11ᶠᶠ 11-11-11ᶠᶠ 11-11-11ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ
00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 11-11-11ᶠᶠ 11-11-11ᶠᶠ 11-11-11ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ
00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ
00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ
00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ 00-00-00ᶠᶠ
`;
