
> @jimp/plugin-cover@1.1.1 test:browser /Users/<USER>/Documents/jimp/plugins/plugin-cover
> vitest --config vitest.config.browser.mjs "--watch=false" "--u"


[7m[1m[36m RUN [39m[22m[27m [36mv1.4.0[39m [90m/Users/<USER>/Documents/jimp/plugins/plugin-cover[39m
[2m[32m      Browser runner started at http://localhost:5174/[39m[22m

[?25l   [90m·[39m All align combinations for cover[2m (18)[22m
     [90m·[39m vertical contain aligned to LEFT TOP
     [90m·[39m horizontal contain aligned to LEFT TOP
     [90m·[39m vertical contain aligned to CENTER TOP
     [90m·[39m horizontal contain aligned to CENTER TOP
     [90m·[39m vertical contain aligned to RIGHT TOP
     [90m·[39m horizontal contain aligned to RIGHT TOP
     [90m·[39m vertical contain aligned to LEFT MIDDLE
     [90m·[39m horizontal contain aligned to LEFT MIDDLE
     [90m·[39m vertical contain aligned to CENTER MIDDLE
     [90m·[39m horizontal contain aligned to CENTER MIDDLE
     [90m·[39m vertical contain aligned to RIGHT MIDDLE
     [90m·[39m horizontal contain aligned to RIGHT MIDDLE
     [90m·[39m vertical contain aligned to LEFT BOTTOM
     [90m·[39m horizontal contain aligned to LEFT BOTTOM
     [90m·[39m vertical contain aligned to CENTER BOTTOM
     [90m·[39m horizontal contain aligned to CENTER BOTTOM
     [90m·[39m vertical contain aligned to RIGHT BOTTOM
     [90m·[39m horizontal contain aligned to RIGHT BOTTOM
[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[?25l[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[1A[2K[G [32m✓[39m [2msrc/[22mindex[2m.test.ts[22m[2m (18)[22m
   [32m✓[39m All align combinations for cover[2m (18)[22m
     [32m✓[39m vertical contain aligned to LEFT TOP
     [32m✓[39m horizontal contain aligned to LEFT TOP
     [32m✓[39m vertical contain aligned to CENTER TOP
     [32m✓[39m horizontal contain aligned to CENTER TOP
     [32m✓[39m vertical contain aligned to RIGHT TOP
     [32m✓[39m horizontal contain aligned to RIGHT TOP
     [32m✓[39m vertical contain aligned to LEFT MIDDLE
     [32m✓[39m horizontal contain aligned to LEFT MIDDLE
     [32m✓[39m vertical contain aligned to CENTER MIDDLE
     [32m✓[39m horizontal contain aligned to CENTER MIDDLE
     [32m✓[39m vertical contain aligned to RIGHT MIDDLE
     [32m✓[39m horizontal contain aligned to RIGHT MIDDLE
     [32m✓[39m vertical contain aligned to LEFT BOTTOM
     [32m✓[39m horizontal contain aligned to LEFT BOTTOM
     [32m✓[39m vertical contain aligned to CENTER BOTTOM
     [32m✓[39m horizontal contain aligned to CENTER BOTTOM
     [32m✓[39m vertical contain aligned to RIGHT BOTTOM
     [32m✓[39m horizontal contain aligned to RIGHT BOTTOM

[2m Test Files [22m [1m[32m1 passed[39m[22m[90m (1)[39m
[2m      Tests [22m [1m[32m18 passed[39m[22m[90m (18)[39m
[2m   Start at [22m 00:40:13
[2m   Duration [22m 2.83s[2m (transform 0ms, setup 0ms, collect 93ms, tests 6ms, environment 0ms, prepare 0ms)[22m

[?25h[?25h
